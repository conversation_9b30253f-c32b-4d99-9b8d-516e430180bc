import endpoints from "@/constants/api";
import { TGetHolidaysInRangeGregorianParams, IGetHolidaysInRangeGregorian } from "@/queries/dateTimeProviderApi/types";
import { useQuery } from "@tanstack/react-query";

export const useGetHolidaysInRangeGregorianQuery = (params?: TGetHolidaysInRangeGregorianParams) =>
  useQuery<IGetHolidaysInRangeGregorian>({
    queryKey: [endpoints.getHolidaysInRangeGregorian({ ...params })]
  });
