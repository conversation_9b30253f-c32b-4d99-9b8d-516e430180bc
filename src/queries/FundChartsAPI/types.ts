import { IBaseResponseType } from "@/types/globalTypes";

export type TPortfolioRow = {
  checkpointDate: string;
  ytm: number;
  ytc: number;
  convexity: number;
  modifiedDuration: number;
  Duration: number;
};

export type THistogram = {
  rangeStartPercent: number;
  rangeEndPercent: number;
  rangeCenterPercent: number;
  rangeStartValue: number;
  rangeEndValue: number;
  rangeCenterValue: number;
  negativeQuantity: number;
  positiveQuantity: number;
  totalQuantity: number;
};

export type TPortfolioYtmRow = {
  checkpointDate: string;
  ytm: number;
};

export type TPortfolioConvexityRow = {
  checkpointDate: string;
  convexity: number;
};

export type TFundData = {
  graphPoints: TPortfolioRow[];
  yearAverage: number;
};

// ************************** Interest Risk **************************
type TFundInterestAverages = {
  yearAverage: number;
  marketYearAverage: number;
};

// 1-Ytm
export type TFundYtmRow = {
  checkpointDate: string;
  ytm: number;
  marketYtm: number;
};
export interface IFundYtmData extends TFundInterestAverages {
  graphPoints: TFundYtmRow[];
}
export interface IGetListOfFundYtm extends IBaseResponseType {
  data: IFundYtmData;
}

// 2-Ytc
export type TFundYtcRow = {
  checkpointDate: string;
  ytc: number;
};
export interface IFundYtcData extends TFundInterestAverages {
  graphPoints: TFundYtcRow[];
}
export interface IGetListOfFundYtc extends IBaseResponseType {
  data: IFundYtcData;
}

// 3-Convexity
export type TFundConvexityRow = {
  checkpointDate: string;
  convexity: number;
};
export interface IFundConvexityData extends TFundInterestAverages {
  graphPoints: TFundDurationRow[];
}
export interface IGetListOfFundConvexity extends IBaseResponseType {
  data: IFundConvexityData;
}

// 4-Duration
export type TFundDurationRow = {
  checkpointDate: string;
  duration: number;
  marketDuration: number;
  marketModifiedDuration: number;
  IFundCouponMarketDurationData: number;
  marketDurationYearAverage: number;
  modifiedDuration: number;
};
export interface IFundDurationData {
  durationYearAverage: number;
  modifiedDurationYearAverage: number;
  marketModifiedDurationYearAverage: number;
  marketDurationYearAverage: number;
  graphPoints: TFundDurationRow[];
}
export interface IGetListOfFundDuration extends IBaseResponseType {
  data: IFundDurationData;
}

// 5-MarketCouponBondsDuration
export type TFundCouponMarketDurationRow = {
  checkpointDate: string;
  duration: number;
  modifiedDuration: number;
};
export interface IFundCouponMarketDurationData {
  durationYearAverage: number;
  modifiedDurationYearAverage: number;
  graphPoints: TFundDurationRow[];
}
export interface IGetListOfFundCouponMarketDuration extends IBaseResponseType {
  data: IFundCouponMarketDurationData;
}
// Histogram
export interface IGetHistogramGraph extends IBaseResponseType {
  data: {
    reportDate: string;
    graphPoints: THistogram[];
  };
}
// 6- MarketValueWeightedAverage
export type TFundMarketValueWeightedAverageRow = {
  checkpointDate: string;
  riskFreeRate: number;
};
export interface IFundMarketValueWeightedAverageData {
  yearAverage: number;
  graphPoints: TFundMarketValueWeightedAverageRow[];
}
export interface IGetListOfFundMarketValueWeightedAverage extends IBaseResponseType {
  data: IFundMarketValueWeightedAverageData;
}

// 7- MarketZeroCoupon
export interface IFundZeroCouponMarketDurationData {
  durationYearAverage: number;
  modifiedDurationYearAverage: number;
  graphPoints: TFundDurationRow[];
}
export interface IGetListOfFundZeroCouponMarketDuration extends IBaseResponseType {
  data: IFundZeroCouponMarketDurationData;
}

// Interest Sensitivity
export interface IFundInterestSensitivityData {
  interestChangePercent: number;
  portfolioValueChange: number;
}
export interface IGetListOfFundInterestSensitivity extends IBaseResponseType {
  data: IFundInterestSensitivityData[];
}

// Interest Sensitivity by Value
export interface IGetListOfFundInterestSensitivityByChange extends IBaseResponseType {
  data: { portfolioValueChange: number }[];
}
// ************************** Market Risk **************************

type TFundAverages = {
  forwardLookingYearAverage: number;
  retrospectiveYearAverage: number;
  simpleYearAverage: number;
};

// 1-Beta
export type TFundBetaRow = {
  checkpointDate: string;
  forwardLookingBeta: number;
  retrospectiveBeta: number;
};

export interface IFundBetaData extends TFundAverages {
  graphPoints: TFundBetaRow[];
}
export interface IGetListOfFundBeta extends IBaseResponseType {
  data: IFundBetaData;
}

// 2-Deviation
export type TFundDeviationRow = {
  checkpointDate: string;
  forwardLookingStandardDeviation: number;
  retrospectiveStandardDeviation: number;
};

export interface IFundDeviationData extends TFundAverages {
  graphPoints: TFundDeviationRow[];
}
export interface IGetListOfFundDeviation extends IBaseResponseType {
  data: IFundDeviationData;
}

// 3-Efficiency
export type TFundEfficiencyRow = {
  checkpointDate: string;
  forwardLookingExpectedReturn: number;
  retrospectiveExpectedReturn: number;
};

export interface IFundEfficiencyData extends TFundAverages {
  graphPoints: TFundEfficiencyRow[];
}
export interface IGetListOfFundEfficiency extends IBaseResponseType {
  data: IFundEfficiencyData;
}

// 4-Trainer
export type TFundTrainerRow = {
  checkpointDate: string;
  forwardLookingTrynor: number;
  retrospectiveTrynor: number;
};

export interface IFundTrainerData extends TFundAverages {
  graphPoints: TFundTrainerRow[];
}
export interface IGetListOfFundTrainer extends IBaseResponseType {
  data: IFundTrainerData;
}

// 5-Sharp
export type TFundSharpRow = {
  checkpointDate: string;
  forwardLookingSharpe: number;
  retrospectiveSharpe: number;
};

export interface IFundSharpData extends TFundAverages {
  graphPoints: TFundSharpRow[];
}
export interface IGetListOfFundSharp extends IBaseResponseType {
  data: IFundSharpData;
}

// 6-Alpha
export type TFundAlphaRow = {
  checkpointDate: string;
  retrospectiveModifiedJensensAlpha: number;
  forwardLookingModifiedJensensAlpha: number;
};

export interface IFundAlphaData extends TFundAverages {
  graphPoints: TFundAlphaRow[];
}
export interface IGetListOfFundAlpha extends IBaseResponseType {
  data: IFundAlphaData;
}

// 7-Risk Values
export type TFundRiskValueRow = {
  checkpointDate: string;
  forwardLookingJensensAlpha: number;
  retrospectiveJensensAlpha: number;
};

export interface IFundRiskValueData extends TFundAverages {
  graphPoints: TFundRiskValueRow[];
  yearAverage: number;
}
export interface IGetListOfFundRiskValue extends IBaseResponseType {
  data: IFundRiskValueData;
}

// Total Asset Values Chart

type TFundTotalValueRow = {
  checkpointDate: string;
  fundTotalValue: number;
};
export interface IFundTotalAssetValuesData {
  graphPoints: TFundTotalValueRow[];
  yearAverage: number;
}
export interface IGetListOfFundTotalAssetValues extends IBaseResponseType {
  data: IFundTotalAssetValuesData;
}

// Focus Risk
export type TFocusRiskChartSerie = {
  checkpointDate: string;
  percentage: number | null;
};

export type TFocusRiskChartSerie2 = {
  checkpointDate: string;
  value: number | null;
};

export type TFocusRiskChartSerie3 = {
  checkPoint: string;
  value: number | null;
};

export type TCRInfoRow = {
  title: string;
  ownershipPercentage: number;
};

export type TDynamicSerieChart = {
  name: string;
  checkable: boolean;
  graphPoints: TFocusRiskChartSerie2[];
};

export type TDynamicSerieChart2 = {
  name: string;
  checkable: boolean;
  graphpoints: TFocusRiskChartSerie2[];
};

// 1-OwnershipPercentag

export interface IFocusRiskDynamicTickerBody {
  fundId: string;
  tickerId: string;
}

export interface IGetFocusRiskOwnershipPercentageCard extends IBaseResponseType {
  data: {
    lastDay: number;
    graphPoints: TFocusRiskChartSerie2[];
  };
}
export interface IGetFocusRiskOwnershipPercentageChart extends IBaseResponseType {
  data: TDynamicSerieChart2[];
}

export interface IGetFocusRiskOwnershipValueChart extends IBaseResponseType {
  data: TDynamicSerieChart2[];
}
export interface IGetFocusRiskGroupsList extends IBaseResponseType {
  data: {
    id: string;
    name: string;
  }[];
}

// 2-HHI Card
export interface IGetFocusRiskHHICard extends IBaseResponseType {
  data: {
    lastValue: number;
    fundsUnitHoldersHHIDiagramDetails: TFocusRiskChartSerie3[];
  };
}

// 3-CR Cards
export interface IGetListOfCRCards extends IBaseResponseType {
  data: {
    cr1: {
      groupOwnerShipPercentage: number;
      yearlyGraph: TFocusRiskChartSerie[];
      unitHolders: TCRInfoRow[];
    };
    cr2: {
      groupOwnerShipPercentage: number;
      yearlyGraph: TFocusRiskChartSerie[];
      unitHolders: TCRInfoRow[];
    };
    cr3: {
      groupOwnerShipPercentage: number;
      yearlyGraph: TFocusRiskChartSerie[];
      unitHolders: TCRInfoRow[];
    };
  };
}

type TCRSerie = {
  checkpointDate: string;
  cr1: number;
  cr2: number;
  cr3: number;
};

export interface IGetListOfCRChart extends IBaseResponseType {
  data: TCRSerie[];
}

type TAssociatedOwnersSerie = {
  checkpointDate: string;
  associatedOwnershipPercent: number;
  notAssociatedOwnershipPercent: number;
};

export interface IGetListOfAssociatedOwnersChart extends IBaseResponseType {
  data: {
    graphPoints: TAssociatedOwnersSerie[];
  };
}

// 2-Dynamic Groups

export type TFocusRiskDynamicGroup = {
  date: string;
  groupOwnershipPercent: number;
  memberSummaries: {
    id: string;
    name: string;
    ownershipPercent: number;
  }[];
};
export interface IGetFocusRiskDynamicGroupsChart extends IBaseResponseType {
  data: TFocusRiskDynamicGroup[];
}

export type TChartParams = {
  FundId: string;
  FromDate?: string;
  ToDate?: string;
};
// focus risk asset

// Bond
export interface IGetFocusRiskAssetBondCR extends IBaseResponseType {
  data: {
    cr1: ICR;
    cr2: ICR;
    cr3: ICR;
  };
}

export interface IGetFocusRiskAssetBondsHHIChart extends IBaseResponseType {
  data: IGetFocusRiskAssetBondHHIChartData;
}

export interface IGetFocusRiskAssetBondHHIChartData {
  lastValue: number;
  fundsBondsHHIDiagramDetails: IFundsBondsHHIDiagramDetailsItem[];
}

export interface IFundsBondsHHIDiagramDetailsItem {
  checkPoint: string;
  fundId: string;
  value: number;
}

export interface IGetFocusRiskAssetBondCRChart extends IBaseResponseType {
  data: IGetFocusRiskAssetBondCRChartData[];
}
export interface IGetFocusRiskAssetBondCRChartData {
  checkpointDate: string;
  cr1: number;
  cr2: number;
  cr3: number;
}

// share
export interface IGetFocusRiskAssetSharesHHIChart extends IBaseResponseType {
  data: IGetFocusRiskAssetSharesHHIChartData;
}

export interface IGetFocusRiskAssetSharesHHIChartData {
  lastValue: number;
  fundsSharesHHIDiagramDetails: IFundsSharesHHIDiagramDetailsItem[];
}

export interface IFundsSharesHHIDiagramDetailsItem {
  checkPoint: string;
  fundId: string;
  value: number;
}

export interface IGetFocusRiskAssetSharesCRChart extends IBaseResponseType {
  data: IGetFocusRiskAssetSharesCRChartData[];
}

export interface IGetFocusRiskAssetSharesCRChartData {
  checkpointDate: string;
  cr1: number;
  cr2: number;
  cr3: number;
}

export interface IGetFocusRiskAssetSharesCR extends IBaseResponseType {
  data: {
    cr1: ICR;
    cr2: ICR;
    cr3: ICR;
  };
}

// industries
export interface IGetFocusRiskAssetIndustriesCR extends IBaseResponseType {
  data: {
    cr1: ICR;
    cr2: ICR;
    cr3: ICR;
  };
}

export interface IGetFocusRiskAssetIndustriesHHIChart extends IBaseResponseType {
  data: IGetFocusRiskAssetIndustriesHHIChartData;
}

export interface IGetFocusRiskAssetIndustriesHHIChartData {
  lastValue: number;
  fundsIndustriesHHIDiagramDetails: IFundsIndustriesHHIDiagramDetailsItem[];
}

export interface IFundsIndustriesHHIDiagramDetailsItem {
  checkPoint: string;
  fundId: string;
  value: number;
}

export interface IGetFocusRiskAssetIndustriesCRChart extends IBaseResponseType {
  data: IGetFocusRiskAssetIndustriesCRChartData[];
}

export interface IGetFocusRiskAssetIndustriesCRChartData {
  checkpointDate: string;
  cr1: number;
  cr2: number;
  cr3: number;
}

export interface ICR {
  instruments: ICrInstruments[];
  yearlyGraph: ICrYearlyGraph[];
  groupOwnerShipPercentage: number;
}

interface ICrInstruments {
  ownershipPercentage: number;
  name: string;
  symbol: string;
}

interface ICrYearlyGraph {
  checkpointDate: string;
  percentage: number;
}

export interface IHHIParams {
  fundId?: string;
  FromDate?: string;
  ToDate?: string;
}

export interface ICRParams {
  fundId?: string;
  FromDate?: string;
  ToDate?: string;
}

export interface IPurchaseDetail {
  purchaseType: number;
  contractNumber: string;
  purchaseDate: string;
  purchasePrice: number;
  amount: number;
  contractDue: string;
  soledAmount: number;
  ytm: number;
  remainingAmount: number;
  contractStatus: number;
}

export interface IPurchase {
  instrumentName?: string;
  isin?: string;
  portfolioSheetsValue?: number;
  purchaseDetails: IPurchaseDetail[];
  symbol?: string;
  ytm?: number;
}

export interface IGetPortfolioWithPurchaseDetail {
  data?: { items?: IPurchase[] };
}

export interface IPiePortfo {
  instrumentName?: string;
  isin?: string;
  symbol?: string;
  portfolioSheetsValue?: number;
  portfolioWeightInPercents?: number;
}

export interface IGetPiePortfolio {
  data?: {
    items?: IPiePortfo[];
    portfolioSheetsValuesSum?: number;
    portfolioPreferredYtm: number;
    portfolioPreferredYtmInPercent: number;
  };
}
