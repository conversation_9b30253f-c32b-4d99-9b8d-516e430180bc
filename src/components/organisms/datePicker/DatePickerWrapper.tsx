import { PopOver } from "@/components/atoms/popper";
import { Ref, forwardRef, useImperativeHandle, useState } from "react";
import { maxDate as MaxDateConst, minDate as MinDateConst } from "@/components/organisms/datePicker/utils";
import { TDatePickerProps } from "headless-react-datepicker";
import { Spinner } from "@/components/atoms/spinner";
import { useGetHolidaysInRangeGregorianQuery } from "@/queries/dateTimeProviderApi/DateTimeProviderApi";
import DatePicker from "./DatePicker";
import { IDatePickerWrapperProps, IDatePickerWrapperRefProps } from "./types";

function DatePickerWrapper<T extends boolean = boolean>(
  {
    children,
    placement = "bottom-start",
    className = "",
    onCancel,
    onConfirm,
    closeOnConfirm = true,
    ...restProps
  }: IDatePickerWrapperProps<T>,
  ref: Ref<IDatePickerWrapperRefProps> // Ref should be of type DatePickerWrapperRef
) {
  const [isOpen, setIsOpen] = useState(restProps?.isOpen ?? false);
  const minDate = restProps?.config?.minDate || MinDateConst;
  const maxDate = restProps?.config?.maxDate || MaxDateConst;

  const { data, isLoading } = useGetHolidaysInRangeGregorianQuery({
    FromDate: minDate?.toISOString(),
    ToDate: maxDate?.toISOString()
  });

  const toggleOpen = (v: any) => setIsOpen(v);

  const handleCancel = () => {
    onCancel?.();
    setIsOpen(false);
  };

  const handleConfirm = (value?: TDatePickerProps<T>["initialValue"]) => {
    onConfirm?.(value);

    if (closeOnConfirm) {
      setIsOpen(false);
    }
  };

  useImperativeHandle(ref, () => ({
    close() {
      setIsOpen(false);
    },
    open() {
      setIsOpen(true);
    }
  }));

  return (
    <PopOver
      setIsOpen={toggleOpen}
      isOpen={isOpen}
      placement={placement}
      className={className}
      content={
        isLoading ? (
          <div
            style={{
              background: "#343438",
              color: "#F4F4F4",
              border: "0.5px solid #B9B4AB",
              width: "288px",
              borderRadius: "8px",
              margin: "0 auto",
              minHeight: "310px"
            }}
            className="flex items-center justify-center"
          >
            <Spinner />
          </div>
        ) : (
          <DatePicker
            setIsOpen={toggleOpen}
            onCancel={handleCancel}
            onConfirm={handleConfirm}
            {...restProps}
            holidays={data?.data?.map(a => new Date(a))}
          />
        )
      }
    >
      {children}
    </PopOver>
  );
}

const ForwardedDatePickerWrapper = forwardRef(DatePickerWrapper) as <T extends boolean = boolean>(
  props: IDatePickerWrapperProps<T> & { ref?: Ref<IDatePickerWrapperRefProps> }
) => React.ReactElement;

export default ForwardedDatePickerWrapper;
