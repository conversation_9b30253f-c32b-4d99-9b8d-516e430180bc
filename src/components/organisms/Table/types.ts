import { IMenuItem, IPosition } from "@/components/molecules/menu";
import { CellContextMenuEvent } from "ag-grid-community";
import { AgGridReactProps } from "ag-grid-react";

export type SortState = {
  colId: string;
  sort: "asc" | "desc" | null | undefined;
  sortIndex: number | null | undefined;
};

export interface ITable<T> extends AgGridReactProps<T> {
  data: T[] | null;
  searchKey?: string;
  menuItems?: IMenuItem<T>[];
  headerColor?: string;
  onSort?: (data?: SortState) => void;
}

export type TOnCellContextEvent<T> = CellContextMenuEvent<T>["event"] & { x: number; y: number };

export interface IMenuOption<T> {
  isOpen: boolean;
  position: IPosition;
  rowData?: T;
}
