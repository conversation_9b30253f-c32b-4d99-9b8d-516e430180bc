import Skeleton, { SkeletonTheme } from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

interface ITableLoadingProps {
  colNumbers: number;
  rowNumbers: number;
}

function TableLoading({ colNumbers, rowNumbers }: ITableLoadingProps) {
  return (
    <div className="bg-[#1F1F22]">
      <SkeletonTheme baseColor="#343438" highlightColor="#3D3D3D" borderRadius="0.5rem" duration={2}>
        {Array.from(Array(colNumbers).keys()).map(row => (
          <div key={row} className="flex w-full gap-2">
            {Array.from(Array(rowNumbers).keys()).map(col => (
              <div className="grow " key={col}>
                <Skeleton className=" my-2.5 h-6 w-40" />
              </div>
            ))}
          </div>
        ))}
      </SkeletonTheme>
    </div>
  );
}

export default TableLoading;
