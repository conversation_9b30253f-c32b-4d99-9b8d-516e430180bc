import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";

import { AgGridReact } from "ag-grid-react";
import { twMerge } from "tailwind-merge";
import { memo, useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import { IMenuOption, ITable, TOnCellContextEvent } from "@/components/organisms/Table/types";
import {
  renderCoupon,
  renderYtm,
  renderYtmPrice,
  renderDate,
  renderTransactionTime,
  renderFloatNumber
} from "@/components/organisms/Table/utils";
import { Menu } from "@/components/molecules/menu";
import { CellContextMenuEvent, SortChangedEvent } from "ag-grid-community";

import styles from "./Table.module.scss";

const Table = forwardRef(
  <T,>(
    {
      data,
      gridOptions,
      onSort,
      columnDefs,
      rowHeight,
      className,
      menuItems,
      headerColor,
      searchKey,
      ...restProps
    }: ITable<T>,
    ref: React.Ref<AgGridReact<T>>
  ) => {
    const gridRef = useRef<AgGridReact<any> | null>(null);
    const [menuOption, setMenuOption] = useState<IMenuOption<T>>({
      isOpen: false,
      rowData: undefined,
      position: { x: 0, y: 0 }
    });

    const onFilterChanged = () => {
      if (searchKey) {
        const rowCount = gridRef.current?.api.getDisplayedRowCount();

        if (rowCount === 0) {
          gridRef.current?.api.showNoRowsOverlay();
        } else {
          gridRef.current?.api.hideOverlay(); // Hide overlay if there are rows
        }
      }
    };

    /* ------------------- set position of right click popover ------------------ */
    const onCellContextMenu = (e: CellContextMenuEvent<T>) => {
      const event = e.event as TOnCellContextEvent<T>;

      setMenuOption(prev => ({
        ...prev,
        isOpen: true,
        rowData: e.data,
        position: { x: event?.x, y: event?.y }
      }));
    };

    useEffect(() => {
      const handleContextMenu = (event: MouseEvent) => {
        if ((event.target as HTMLElement).closest(".ag-row-pinned")) {
          event.preventDefault();
        }
      };

      document.addEventListener("contextmenu", handleContextMenu);

      return () => {
        document.removeEventListener("contextmenu", handleContextMenu);
      };
    }, []);

    useImperativeHandle(
      ref,
      () =>
        ({
          ...gridRef.current,
          onSearch: (value: string) => {
            // if (!value) {
            //   setRowData(props?.rowData);
            // }
            if (!searchKey) return;

            gridRef.current?.api.setFilterModel({
              [searchKey]: { type: "contains", filter: value }
            });
          }
        }) as any
    );

    const handleSort = (e: SortChangedEvent<any, any>) => {
      const sortState = e.columnApi
        .getColumnState()
        ?.filter(s => s.sort != null)
        ?.map(s => ({ colId: s.colId, sort: s.sort, sortIndex: s.sortIndex }))
        ?.at(0);

      onSort?.(sortState);
    };

    return (
      <div
        className="h-full"
        style={
          {
            "--header-color": headerColor || "#343438"
          } as React.CSSProperties
        }
      >
        <AgGridReact<T>
          animateRows
          enableRtl
          icons={{
            sortAscending: `<img src="/sort-selected.svg" class="sort-selected-icon" alt="svg"/>`,
            sortDescending: `<img src="/sort-selected.svg" class="sort-selected-icon" alt="svg"/>`
          }}
          preventDefaultOnContextMenu
          onCellContextMenu={onCellContextMenu}
          enableCellChangeFlash
          suppressColumnVirtualisation
          rowHeight={rowHeight || 64}
          rowData={data}
          onFilterChanged={onFilterChanged}
          onSortChanged={restProps?.onSortChanged || handleSort}
          gridOptions={gridOptions}
          columnDefs={columnDefs}
          components={{
            renderYtm,
            renderYtmPrice,
            renderCoupon,
            renderDate,
            renderTransactionTime,
            renderFloatNumber
          }}
          ref={gridRef}
          className={twMerge(
            "ag-theme-alpine",
            styles.tableContainer,
            !data?.length && styles.disableHeader,
            data === null && styles.tableLoading,
            className
          )}
          {...restProps}
        />
        {!!menuItems?.length && (
          <Menu
            {...menuOption}
            items={menuItems}
            onClose={() =>
              setMenuOption(prev => ({
                ...prev,
                isOpen: false
              }))
            }
          />
        )}
      </div>
    );
  }
);

export default memo(Table);
