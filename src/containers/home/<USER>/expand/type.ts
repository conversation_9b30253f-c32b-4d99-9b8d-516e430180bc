import { TTodayAverageResponse, TTotalValueDataResponse, TTotalValueDurationResponse } from "@/queries/homeCards/types";
import { ReactNode } from "react";

export type XYType = [string, number][];
export interface IMiniChartInfoCard {
  title?: string;
  chartSectionClassName?: string;
  infoSectionClassName?: string;
  isPercentage?: boolean;
  link: string;
  chartData1?: XYType;
  chartData2?: XYType;
  loading?: boolean;
  tooltipContent?: ReactNode;
  children?: ReactNode;
  chartLineColor?: string;
}

export interface IGeneralStatisticsProps {
  tradeValues?: TTotalValueDataResponse;
  durationValues?: TTotalValueDurationResponse;
  tradeValuesLoading: boolean;
  duraionLoading: boolean;
  todayAverage?: TTodayAverageResponse;
  averageLoading: boolean;
}
