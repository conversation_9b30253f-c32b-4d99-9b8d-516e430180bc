"use client";

import Info from "@/assets/icons/Info.svg";
import Link from "next/link";
import Spinner from "@/assets/spinner.svg";
import { Tooltip } from "@/components/atoms/tooltip";
import { twMerge } from "tailwind-merge";
import Pattern from "@/assets/icons/Pattern-expand.svg";
import { ChartSection } from "./components";
import { IMiniChartInfoCard } from "./type";

function MiniChartInfoCard({
  title,
  chartSectionClassName,
  infoSectionClassName,
  link,
  chartData1,
  chartData2,
  loading,
  tooltipContent,
  children,
  chartLineColor
}: IMiniChartInfoCard) {
  return (
    <Link
      href={link}
      className={twMerge(
        "flex w-full relative pb-px rounded-lg h-[152px] border border-transparent overflow-hidden",
        infoSectionClassName
      )}
    >
      <ChartSection
        loading={loading}
        chartData1={chartData1}
        chartData2={chartData2}
        link={link}
        className={chartSectionClassName}
        chartLineColor={chartLineColor}
      />

      <div className={twMerge("flex-1 bg-center rounded ", infoSectionClassName)}>
        <Pattern className="absolute left-0 top-0 rounded-l-lg overflow-hidden" />
        {loading ? (
          <div className="flex items-center justify-center pt-14">
            <Spinner className="animate-spin p-0.5 h-8 w-8" />
          </div>
        ) : (
          <>
            <div className="flex justify-between items-center pt-2 px-4">
              <div className="font-normal text-sm leading-[24px]">{title}</div>
              {tooltipContent && (
                <Tooltip
                  content={tooltipContent}
                  placement="bottom"
                  // eslint-disable-next-line react/no-children-prop
                  children={
                    <div className="z-10" data-test="5a17115e-0fa2-4064-a2c4-b61fb78ac5d3">
                      <Info className="h-4 w-4" />
                    </div>
                  }
                />
              )}
            </div>
            {children}
          </>
        )}
      </div>
    </Link>
  );
}

export default MiniChartInfoCard;
