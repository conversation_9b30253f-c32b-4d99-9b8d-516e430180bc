import { XYType } from "@/containers/home/<USER>/expand/type";

export interface NameValuePairProps {
  label?: string;
  value?: number | string | JSX.Element;
  change?: string;
  className?: string;
  withTimeCheck?: boolean;
  hasCoupon?: boolean;
}

export interface IChartSectionProps {
  className?: string;
  link: string;
  chartData1?: XYType;
  chartData2?: XYType;
  loading?: boolean;
  chartLineColor?: string;
}
