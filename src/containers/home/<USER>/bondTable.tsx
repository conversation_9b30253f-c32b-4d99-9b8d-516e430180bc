/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/button-has-type */
/* eslint-disable no-nested-ternary */
import useToggleCollapse from "@/app/(home)/CollapseStore";
import Maximaze from "@/assets/icons/maximaze.svg";
import Excel from "@/assets/icons/excel.svg";
import Pagination from "@/components/molecules/pagination/Pagination";
import { Table } from "@/components/organisms/Table";
import routes from "@/constants/routes";
import useCalculatorStore, { useCalculatorStatus } from "@/store/CalendarStore";

import {
  BOND_TABLE,
  BOND_TABLE_PIN,
  TBondTableResponse,
  useAddToPinMutation,
  useGetBondTableAllQuery,
  useGetBondTablePinQuery,
  useGetBondTableQuery,
  useRemovePinMutation
} from "@/queries/bondTableAPI";
import socketUrls from "@/constants/socketUrls";
import useFilterStore from "@/store/FilterStore";
import { ConvertEnToFa } from "@/utils/helpers";
import { CellDoubleClickedEvent, GridOptions } from "ag-grid-community";
import { useEffect, useMemo, useState } from "react";
import exportData from "@/utils/excel";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import Calculator from "@/assets/icons/menu-calculator.svg";
import Eye from "@/assets/icons/menu-eye.svg";
// import Telescope from "@/assets/icons/menu-telescope.svg";
import useDelayedStack from "@reactutils/use-delayed-stack";
import Pin from "@/assets/icons/menu-pin.svg";
import { errorToast } from "@/utils/toast";
import UnPin from "@/assets/icons/menu-unpin.svg";
import { useQueryClient } from "@tanstack/react-query";
import { usePostAddSymbolToWatchListMutation } from "@/queries/watchListAPI";
import { addToWatchList } from "@/containers/watchList/page";
import { SortState } from "@/components/organisms/Table/types";
import { getRowId } from "@/components/organisms/Table/utils";
import { CustomLoadingOverlay, columnDefs, selectExcelFields, sortKeyName, updateTableData } from "./utils";
import { IBondTableSocket, TBoundTableSocket } from "./types";
import NoData from "./NoData";

function exportExcel(data?: any[]) {
  exportData(data, { fileName: "Bonds", sheetName: "Bonds", extension: "xlsx" });
}

function BondTable() {
  const queryClient = useQueryClient();
  const { isCollapsed, toggleCollapse } = useToggleCollapse();
  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(15);

  const [sorts, setSorts] = useState<{ [key: string]: string }>({ SortOrderTotalTradedVolume: "Desc" });

  const {
    CouponBond,
    ZeroCouponBond,
    MaturityDateRange,
    LastTradePriceYtm,
    ClosePriceYtm,
    BestBuyYtm,
    BestSellYtm,
    TotalTradedVolume,
    NominalInterestRate,
    BondName,
    isConfirmFilterCount
  } = useFilterStore();

  const couponType =
    CouponBond && ZeroCouponBond ? { BondType: "0" } : { BondType: CouponBond ? "100" : ZeroCouponBond ? "200" : "0" };

  const notHaveCouponType = useMemo(
    () => isConfirmFilterCount && !CouponBond && !ZeroCouponBond,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isConfirmFilterCount]
  );

  const [params, setParams] = useState({
    ...couponType,
    ...sorts,
    pageSize: 15,
    pageNumber: pageNumber + 1,
    MaturityDateFrom: MaturityDateRange?.[0],
    MaturityDateTo: MaturityDateRange?.[1],
    LastTradePriceYtmFrom: LastTradePriceYtm?.[0],
    LastTradePriceYtmTo: LastTradePriceYtm?.[1],
    ClosePriceYtmFrom: ClosePriceYtm?.[0],
    ClosePriceYtmTo: ClosePriceYtm?.[1],
    BestBuyYtmFrom: BestBuyYtm?.[0],
    BestBuyYtmTo: BestBuyYtm?.[1],
    BestSellYtmFrom: BestSellYtm?.[0],
    BestSellYtmTo: BestSellYtm?.[1],
    TotalTradedVolumeFrom: TotalTradedVolume?.[0],
    TotalTradedVolumeTo: TotalTradedVolume?.[1],
    NominalInterestRateFrom: NominalInterestRate?.[0],
    NominalInterestRateTo: NominalInterestRate?.[1],
    BondName
  });

  // if user keyboard is in EN
  const faParams = {
    ...params,
    BondName: ConvertEnToFa(params.BondName)
  };

  const { data: bondTable, isError, isLoading } = useGetBondTableQuery(faParams);
  const { mutateAsync: loadAllData } = useGetBondTableAllQuery();

  const { data: tablePins } = useGetBondTablePinQuery(sorts);
  const { mutate: removePin } = useRemovePinMutation();
  const { mutate: addPin } = useAddToPinMutation();

  const hasPinned = (isin: string) => tablePins?.data?.items?.find(item => item.isin === isin);

  const columnDefsData = useMemo(() => columnDefs({ pinItems: tablePins?.data?.items }), [tablePins?.data?.items]);
  const totalCount = bondTable?.data?.totalCount;

  const getTableData = () => {
    if (notHaveCouponType) return [];
    if (isLoading) return null;
    if (bondTable?.data?.items?.length) return bondTable?.data?.items;

    return [];
  };
  const tableData = getTableData();

  const { setTickerValues } = useCalculatorStore();
  const { showCalculator } = useCalculatorStatus();

  const [pushToStack] = useDelayedStack((data: TBoundTableSocket[]) => {
    updateTableData({ data, queryClient, queryKey: [BOND_TABLE, faParams] });
    updateTableData({ data, queryClient, queryKey: [BOND_TABLE_PIN, sorts] });
  }, 0);

  useEffect(() => {
    setParams({
      ...couponType,
      ...sorts,
      pageSize,
      pageNumber: pageNumber + 1,
      MaturityDateFrom: MaturityDateRange?.[0],
      MaturityDateTo: MaturityDateRange?.[1],
      LastTradePriceYtmFrom: LastTradePriceYtm?.[0],
      LastTradePriceYtmTo: LastTradePriceYtm?.[1],
      ClosePriceYtmFrom: ClosePriceYtm?.[0],
      ClosePriceYtmTo: ClosePriceYtm?.[1],
      BestBuyYtmFrom: BestBuyYtm?.[0],
      BestBuyYtmTo: BestBuyYtm?.[1],
      BestSellYtmFrom: BestSellYtm?.[0],
      BestSellYtmTo: BestSellYtm?.[1],
      TotalTradedVolumeFrom: TotalTradedVolume?.[0],
      TotalTradedVolumeTo: TotalTradedVolume?.[1],
      NominalInterestRateFrom: NominalInterestRate?.[0],
      NominalInterestRateTo: NominalInterestRate?.[1],
      BondName
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isConfirmFilterCount, pageNumber, sorts, pageSize]);

  useEffect(() => {
    setPageNumber(0);
  }, [isConfirmFilterCount, pageSize]);

  useSignalR(socketUrls.priceChangeYtmUrl, socketUrls.priceChangeYtmStreamName, {
    next: ({ message: { instrument, limitOrders, ...restMessages } }: IBondTableSocket) => {
      pushToStack({
        instrument,
        ...restMessages
      } as TBoundTableSocket);
    },
    error: () => {}
  });

  const onSort = (sortState?: SortState) => {
    let sortType = !!sorts && Object.values(sorts)?.at(0);
    // eslint-disable-next-line no-nested-ternary
    sortType = !sortType ? "Asc" : sortType === "Asc" ? "Desc" : "Asc";

    const sort = { [sortKeyName[sortState?.colId ?? ""]]: sortType };
    setSorts(sort);
  };

  const onToggle = () => {
    toggleCollapse(!isCollapsed);
  };

  const onExportClick = () => {
    loadAllData().then((res: TBondTableResponse) => {
      const data = selectExcelFields(res);
      exportExcel(data);
    });
  };

  const onPin = (value: any) => {
    if (hasPinned(value?.isin)) removePin({ isin: value.isin });
    else {
      if (tablePins?.data?.items?.length && tablePins?.data?.items?.length >= 5) {
        errorToast({
          title: "تا پنج نماد را می‌توانید پین کنید!"
        });
        return;
      }
      addPin({ isin: value.isin });
    }
  };

  const { mutateAsync: postAddSymbolToWatchList } = usePostAddSymbolToWatchListMutation(params);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const items = [
    {
      id: 1,
      title: () => "مشاهده جزئیات اوراق",
      icon: () => <Eye />,
      action: (value: any) => {
        window.open(`${routes.detail}/${value?.isin}`, "_blank");
      }
    },
    {
      id: 2,
      title: () => "ماشین حساب",
      icon: () => <Calculator />,
      action: (v: any) => {
        setTickerValues(v.isin, v.symbol);
        showCalculator();
      }
    },
    // {
    //   id: 3,
    //   title: "افزودن به دیدبان",
    //   icon: <Telescope />
    //   // action: value => console.log("value", value)
    // },
    {
      id: 4,
      title: (value: any) => (hasPinned(value?.isin) ? "حذف پین" : "پین در بالای جدول"),
      icon: (value: any) => (hasPinned(value?.isin) ? <UnPin /> : <Pin />),
      action: (value: any) => onPin(value)
    },
    {
      id: 5,
      title: () => "افزودن به دیدبان",
      icon: (value: any) => (hasPinned(value?.isin) ? <UnPin /> : <Pin />),
      action: (value: any) => addToWatchList(value?.isin, value?.symbol, postAddSymbolToWatchList)
    }
  ];

  const gridOptions: GridOptions = {
    enableRtl: true,
    sortingOrder: ["asc", "desc"],
    noRowsOverlayComponent: isError ? (
      <span className="pt-[150px] text-white">ارتباط با سرور برقرار نیست!</span>
    ) : (
      isLoading && CustomLoadingOverlay
    ),

    onGridSizeChanged: () => {
      gridOptions.api?.sizeColumnsToFit();
    }
  };

  if (isError) {
    <div className="flex justify-center items-center grow">
      <span>خطایی رخ داده است!</span>
    </div>;
  }

  return (
    <div className="grow  flex flex-col">
      {isLoading || (!isLoading && tableData?.length) ? (
        <Table
          columnDefs={columnDefsData}
          onSort={onSort}
          menuItems={items}
          gridOptions={gridOptions}
          pinnedTopRowData={tablePins?.data?.items}
          data={tableData}
          getRowId={getRowId as any}
          loadingOverlayComponent={CustomLoadingOverlay}
          noRowsOverlayComponent={CustomLoadingOverlay}
          onCellDoubleClicked={(cell: CellDoubleClickedEvent<any>) =>
            window.open(`${routes.detail}/${cell.data?.isin}`, "_blank")
          }
        />
      ) : (
        !isLoading && !tableData?.length && <NoData isError={isError} columnDefs={columnDefsData} />
      )}

      <div className="h-[40px] bg-[#343438] border-t border-[#545454] justify-between rounded-b-[4px] flex items-center px-2">
        <Maximaze
          className="cursor-pointer w-6 h-6"
          onClick={onToggle}
          data-test="659780b4-fb9c-406e-a04b-edd846c62a63"
        />
        {!!totalCount && totalCount > 0 && (
          <Pagination
            showItemsCounter
            setPageNumber={setPageNumber}
            pageNumber={pageNumber}
            totalCount={totalCount ?? 0}
            rowPerPage={pageSize}
            onChangeRow={setPageSize}
          />
        )}
        <Excel className="cursor-pointer w-6 h-6" onClick={onExportClick} />
      </div>
    </div>
  );
}

export default BondTable;
