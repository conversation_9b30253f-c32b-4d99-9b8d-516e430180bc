/* eslint-disable no-plusplus */
import NewTabIcon from "@/assets/icons/new-tab.svg";
import EmptyGrid from "@/assets/icons/sensitivity-empty.svg";
import { useGetInterestSensitivitiesByChangeChart } from "@/queries/FundChartsAPI";
import useUserModalStore from "@/store/userModalStore/UserModalStore";
import { ColumnChart } from "@/components/molecules/columnChart";
import Styles from "../charts/chartLayouts/lineChart.module.scss";
import { SensitivityChart } from "../charts/interestRisk/SensitivityChart";
import { getSensitivCardOption } from "../charts/interestRisk/SensitivityChart/util";

function InterestRateSensitivityCard() {
  const { openUserModal } = useUserModalStore();
  const { data: sensitiveQuery } = useGetInterestSensitivitiesByChangeChart();
  const { data: sensitiveData } = sensitiveQuery || {};

  const mainChartData = sensitiveData?.map(i => i.portfolioValueChange / 10) || [];
  const categories = ["-3", "-2", "-1", "-0.5", "0", "0.5", "1", "2", "3"];
  const isEmpty = mainChartData.length === 0;

  const seriesMainChart = [
    {
      data: mainChartData?.map((item: number) => (item === 0 ? null : item)),
      color: "#108554",
      negativeColor: "#E35050"
    }
  ];

  const optionsMainChart = getSensitivCardOption({ series: seriesMainChart, categories });

  const onClickMaximize = () => {
    openUserModal(<SensitivityChart />, {
      center: true,
      width: "920px",
      height: "602px",
      className: "bg-backgroundCardBackground rounded-xl",
      rootClassName: Styles.modalSensitivityChart
    });
  };

  return (
    <div
      className="flex flex-col items-center  rounded-lg w-full h-[204px]  bg-sensitivityCard bg-dark_black cursor-pointer  border-2 border-cardBackground hover:border-borderBorderAndDivider"
      onClick={onClickMaximize}
      aria-label="Interest Rate Sensitivity Card"
    >
      <div className="flex w-full justify-between px-2 pt-2">
        <h5 className="text-sm font-bold leading-6">حساسیت نرخ بهره</h5>
        <NewTabIcon width={16} height={16} />
      </div>
      {isEmpty ? (
        <EmptyGrid />
      ) : (
        <div className="relative w-full grow">
          <div className="px-[11px] w-full absolute top-[9px]">
            <span className="flex w-full border-t h-[1px] border-dark_black12 border-dashed" />
          </div>
          <div className="px-[11px] w-full absolute bottom-[14px]">
            <span className="flex w-full border-t h-[1px] border-dark_black12 border-dashed" />
          </div>
          <ColumnChart showResetZoom={false} options={optionsMainChart} className="relative w-full h-full z-10" />
        </div>
      )}
    </div>
  );
}

export default InterestRateSensitivityCard;
