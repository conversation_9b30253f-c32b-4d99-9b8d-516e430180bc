import { parseAsBoolean, useQueryStates } from "nuqs";
import { useState } from "react";
import { toDecimals, toFixedNumber } from "@/utils/helpers";
import { IRadioButtonItem } from "@/components/molecules/radioGroup/type";
import RadioGroup from "@/components/molecules/radioGroup/RadioGroup";
import { twMerge } from "tailwind-merge";
import CircleGauge from "@/components/atoms/circleGauge";
import InfoCardTooltip from "./InfoCardTooltip";
import { isNullOrEmpty } from "./util";
import { IGaugeInterestCardProps } from "./type";
import { FUND_INTEREST } from "../interestRateRisk/types";

function GaugeInterestDurationCard(props: IGaugeInterestCardProps) {
  const { id, type, isSelected, title, desc, structor = [], market, value, onCardClick } = props;
  const [activeRadio, setActiveRadio] = useState(FUND_INTEREST.DURATION.toString());

  const [queryStates] = useQueryStates({
    isShowMarketValue: parseAsBoolean.withDefault(true)
  });
  const { isShowMarketValue } = queryStates;

  const marketFormated = toFixedNumber(market || 0, 3);
  const valueFormated = toFixedNumber(value || 0, 3);
  const values = isShowMarketValue ? [valueFormated, marketFormated] : [valueFormated];

  const unit = "";
  const tooltipData = [];

  tooltipData.push({ color: "#8871BA", name: "پرتفو", y: !isNullOrEmpty(value) ? toDecimals(value || 0, 2) : "-" });

  if (isShowMarketValue) {
    tooltipData.push({
      color: "white",

      name: "بازار",
      y: !isNullOrEmpty(market) ? toDecimals(market || 0, 2) : "-"
    });
  }

  const radioItems = [
    {
      id: FUND_INTEREST.DURATION.toString(),
      label: "مک کالی",
      checked: [type, activeRadio].includes(FUND_INTEREST.DURATION) && type !== FUND_INTEREST.DURATION_MODIFIED
    },
    {
      id: FUND_INTEREST.DURATION_MODIFIED.toString(),
      label: "تعدیل شده",
      checked: [type, activeRadio].includes(FUND_INTEREST.DURATION_MODIFIED)
    }
  ];

  const switchRadio = (v: IRadioButtonItem) => {
    setActiveRadio(v.id.toString());
    onCardClick(v.id.toString());
  };

  const onClickHandle = () => onCardClick(activeRadio);
  const stopPropagation = (e: any) => e.stopPropagation();

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
    <div
      onClick={onClickHandle}
      className={twMerge(
        "flex flex-col h-full rounded-lg p-2 pb-[8px] justify-between cursor-pointer bg-dark_black  border-2 border-cardBackground hover:border-borderBorderAndDivider",
        isSelected && "border-2 !border-mainBlue"
      )}
      aria-label={`Gauge Interest Duration Card ${id}`}
    >
      <div className="flex flex-col gap-1">
        <div className="flex justify-between">
          <h5 className="text-sm leading-6 font-bold">{title}</h5>
          <InfoCardTooltip id={id} data={tooltipData} unit={unit} />
        </div>
        <span className="text-xs leading-4 text-secondaryText">{desc}</span>
      </div>
      <div className="flex justify-center items-start">
        <div className="relative top-0 w-[73px] h-[44px]" onClick={stopPropagation}>
          <RadioGroup items={radioItems} onSwitch={switchRadio} size="medium" className="flex gap-2 flex-col" />
        </div>
        <div className="flex grow justify-end">
          <CircleGauge
            size="medium"
            data={structor}
            value={values}
            arrowColors={["#8871BA", "white"]}
            insideCircleClassName="bg-dark_black"
            labelFormatter={item => item?.min?.toString()}
            lastLabel={
              structor?.length > 0
                ? structor?.sort((a, b) => Number(a?.max) - Number(b?.max))?.[structor.length - 1]?.max
                : ""
            }
            isSelected={isSelected}
          />
        </div>
      </div>
    </div>
  );
}

export default GaugeInterestDurationCard;
