/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import DiagramIcon from "@/assets/icons/diagram.svg";
import { toFixedInPercent } from "@/utils/helpers";
// import { useGetMarketInterestRiskValuesQuery } from "@/queries/fundsAPI";
import { useSocketInitialData } from "@/hooks/useSocketInitialData";
import socketUrls from "@/constants/socketUrls";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import useDelayedStack from "@reactutils/use-delayed-stack";
import { toFixed } from "../tradeHistory/util";
import { IInterestRiskMarketCardData, IInterestRiskMarketCardResponse } from "./type";
import { isNullOrEmpty } from "./util";

function InterestRiskMarketCard(props: { onCardClick: () => void }) {
  const { onCardClick } = props;

  const { data: fundAssets, setData } = useSocketInitialData<IInterestRiskMarketCardResponse>({
    url: socketUrls.interestRiskMarketCardInvoke,
    streamName: socketUrls.interestRiskMarketCardStreamNameInvoke,
    isInvoke: true
  });

  function updateReactQueryTradeValueData(socketData: IInterestRiskMarketCardData[]) {
    const fundAssetsData = fundAssets?.data ? fundAssets?.data : {};

    Object.keys(fundAssetsData).forEach(key => {
      const updatedItem = socketData.find(item => (item as any)?.[key] !== (fundAssetsData as any)?.[key]);

      const updatedBaseArray = updatedItem || fundAssetsData;

      setData({ data: updatedBaseArray } as IInterestRiskMarketCardResponse);
    });
  }

  const [pushToStackMarketValue] = useDelayedStack((data: IInterestRiskMarketCardData[]) => {
    updateReactQueryTradeValueData(data);
  }, 300);

  useSignalR(socketUrls.interestRiskMarketCard, socketUrls.interestRiskMarketCardStreamName, {
    next: (item: any) => {
      pushToStackMarketValue(item);
    },
    error: () => {}
  });

  // eslint-disable-next-line no-nested-ternary
  const fundAssetsData = fundAssets;
  const { data: riskValues } = fundAssetsData || {};

  const {
    couponBondsDuration,
    couponBondsModifiedDuration,
    riskFreeRateIndex,
    zeroCouponBondsDuration,
    zeroCouponBondsModifiedDuration
  } = riskValues || {};

  return (
    <div
      onClick={onCardClick}
      className="cursor-pointer h-full p-2 flex flex-col justify-between rounded-lg border-2 border-backgroundCardBackground hover:border-borderBorderAndDivider bg-dark_black text-borderLightGray"
      aria-label="Interest Risk Market Card"
    >
      <div className="flex flex-col">
        <div className="flex justify-between">
          <h5 className="text-sm font-bold leading-[25px]">اطلاعات بازار بدهی</h5>
          <DiagramIcon width="24" height="24" className="-ml-1.5 -mt-1.5" />
        </div>
        <div className="flex items-center gap-2 mt-[6px] text-xs leading-4 rounded-[4px] text-textSecondary">
          <span>نرخ بهره بدون ریسک:</span>
          <span className="text-xs ltr">
            {!isNullOrEmpty(riskFreeRateIndex) ? `${toFixedInPercent(riskFreeRateIndex || 0, 2)}` : "---"}
          </span>
        </div>
      </div>

      <div className="flex gap-3 text-xs">
        <div className="flex flex-col grow basis-0 px-3 rounded bg-dark_black9">
          <h6 className="leading-10 text-secondaryText">دیرش اوراق کوپن ‌دار</h6>
          <div className="flex justify-between leading-9 text-light_blue2">
            <span>مک کالی</span>
            <span>{!isNullOrEmpty(couponBondsDuration) ? toFixed(couponBondsDuration || 0) : "---"}</span>
          </div>
          <div className="flex justify-between relative top-[-3px] leading-9 text-Firefly400">
            <span className="">تعدیل شده</span>
            <span>
              {!isNullOrEmpty(couponBondsModifiedDuration) ? toFixed(couponBondsModifiedDuration || 0) : "---"}
            </span>
          </div>
        </div>

        <div className="flex flex-col grow basis-0 px-3 rounded bg-dark_black9">
          <h6 className="leading-10 text-secondaryText">دیرش اوراق بدون کوپن</h6>
          <div className="flex justify-between leading-9 text-light_blue2">
            <span>مک کالی</span>
            <span>{!isNullOrEmpty(zeroCouponBondsDuration) ? toFixed(zeroCouponBondsDuration || 0) : "---"}</span>
          </div>
          <div className="flex justify-between relative top-[-3px] leading-9 text-Firefly400">
            <span className="">تعدیل شده</span>
            <span>
              {!isNullOrEmpty(zeroCouponBondsModifiedDuration) ? toFixed(zeroCouponBondsModifiedDuration || 0) : "---"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default InterestRiskMarketCard;
