"use client";

/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/button-has-type */
/* eslint-disable no-nested-ternary */
import AddIcon from "@/assets/icons/addIcon.svg";
import Excel from "@/assets/icons/excel.svg";
import Eye from "@/assets/icons/menu-eye.svg";
import Microscope from "@/assets/icons/microscope.svg";
import TickSuccess from "@/assets/icons/tickSuccess.svg";
import Pagination from "@/components/molecules/pagination/Pagination";
import { Table } from "@/components/organisms/Table";
import routes from "@/constants/routes";
import socketUrls from "@/constants/socketUrls";
import useSignalR from "@/hooks/useSignalR/useSignalR";
import { BOND_TABLE, TBondTableResponse } from "@/queries/bondTableAPI";
import {
  IAddSymbolPost,
  TBondTableData,
  useDeleteSymbolFromWatchListMutation,
  useGetWatchListAllQuery,
  useGetWatchListQuery,
  useGetWatchListSearchQuery,
  usePostAddSymbolToWatchListMutation
} from "@/queries/watchListAPI";
import exportData from "@/utils/excel";
import { MAX_ROWS } from "@/utils/helpers";
import { errorToast, successToast } from "@/utils/toast";
import useDelayedStack from "@reactutils/use-delayed-stack";
import { useQueryClient } from "@tanstack/react-query";
import { CellDoubleClickedEvent, GridOptions } from "ag-grid-community";
import { useEffect, useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import { SortState } from "@/components/organisms/Table/types";
import { getRowId } from "@/components/organisms/Table/utils";
import SideBar from "../marketMap/components/SideBar/SideBar";
import NoData from "./components/NoData/NoData";
import WatchListIsinInput from "./components/WatchListIsinInput/WatchListIsinInput";
import { IIsinInputSearchItem } from "./components/WatchListIsinInput/type";
import { IBondTableSocket, IBoundTableLimitOrder } from "./types";
import { CustomLoadingOverlay, columnDefs, selectExcelFields, sortKeyName } from "./utils";

type TBoundTableSocket = {} & Omit<IBondTableSocket["message"], "limitOrders"> & IBoundTableLimitOrder;

function exportExcel(data?: any[]) {
  exportData(data, { fileName: "Bonds", sheetName: "Bonds", extension: "xlsx" });
}

export const addToWatchList = async (isin: string, symbol: string, postFn: (v: any) => any, refetch?: () => any) => {
  postFn({ isin: isin ?? "" })
    .then(() => {
      successToast({
        title: `نماد ${symbol} با موفقیت به دیدبان اضافه شد`,
        successIcon: (
          <div className="w-6 h-6 rounded bg-inputFill flex justify-center items-center">
            <TickSuccess className="w-3 h-[9px]" />
          </div>
        )
      });
      refetch?.();
    })
    .catch(() => {
      errorToast({
        title: `نماد ${symbol} قبلا افزوده شده است.`
      });
    });
};

function WatchList() {
  const [couponSideBarId, setCouponSideBarId] = useState<string>("");
  const queryClient = useQueryClient();
  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(15);

  const [sorts, setSorts] = useState<{ [key: string]: string }>({ SortOrderBondName: "Asc" });

  const searchParams = {
    pageNumber: 1,
    pageSize: MAX_ROWS
  };
  const { data: watchListDataSearch, refetch } = useGetWatchListSearchQuery(searchParams);

  const [isinInputValue, setIsinInputValue] = useState<IIsinInputSearchItem>();

  const [params, setParams] = useState({
    ...sorts,
    pageSize,
    pageNumber: pageNumber + 1
  });

  const { mutateAsync: postAddSymbolToWatchList } = usePostAddSymbolToWatchListMutation(params);

  const { mutateAsync: deleteSymbol } = useDeleteSymbolFromWatchListMutation(params);

  const deleteSymbolFn = (v: TBondTableData) => {
    errorToast({
      title: `نماد ${v?.symbol} از دیدبان حذف شد`
    });
    deleteSymbol(v?.isin).then(() => {
      refetch();
    });
    setIsinInputValue(undefined);
  };
  const columnDefsData = useMemo(() => columnDefs(deleteSymbolFn, setCouponSideBarId), []);

  const { data: bondTable, isError, isLoading } = useGetWatchListQuery(params);
  const { mutateAsync: loadAllData } = useGetWatchListAllQuery();

  const tableData = isLoading ? null : bondTable?.data?.items?.length ? bondTable?.data?.items : [];
  const totalCount = bondTable?.data?.totalCount;

  const [pushToStack] = useDelayedStack((data: TBoundTableSocket[]) => {
    const cacheData = queryClient.getQueryData<TBondTableResponse>([BOND_TABLE, params]);

    const updatedData = cacheData?.data?.items
      ?.map(item => {
        const socketVal = data.find(v => v.instrument.isin === item.isin);

        if (socketVal) {
          return {
            ...item,
            ...socketVal
          };
        }

        return item;
      })
      ?.filter(Boolean);

    queryClient.setQueryData([BOND_TABLE, params], {
      ...cacheData,
      data: {
        ...cacheData?.data,
        items: updatedData
      }
    });
  }, 300);

  useEffect(() => {
    setParams({
      ...sorts,
      pageSize,
      pageNumber: pageNumber + 1
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNumber, sorts, pageSize]);

  useEffect(() => {
    setIsinInputValue(undefined);
  }, [bondTable]);

  useSignalR(socketUrls.priceChangeYtmUrl, socketUrls.priceChangeYtmStreamName, {
    next: ({ message: { instrument, limitOrders, ...restMessages } }: IBondTableSocket) => {
      const orders = limitOrders?.length ? limitOrders[0] : restMessages;

      pushToStack({ instrument, ...orders } as TBoundTableSocket);
    },
    error: () => {}
  });

  const onSort = (sortState?: SortState) => {
    let sortType = !!sorts && Object.values(sorts)?.at(0);
    // eslint-disable-next-line no-nested-ternary
    sortType = !sortType ? "Asc" : sortType === "Asc" ? "Desc" : "Asc";

    const sort = { [sortKeyName[sortState?.colId ?? ""]]: sortType };
    setSorts(sort);
  };
  const onExportClick = () => {
    loadAllData().then((res: TBondTableResponse) => {
      const data = selectExcelFields(res);
      exportExcel(data);
    });
  };

  const onSubmit = async (data: IAddSymbolPost) => {
    postAddSymbolToWatchList(data as IAddSymbolPost);
  };

  const gridOptions: GridOptions = {
    enableRtl: true,
    sortingOrder: ["asc", "desc"],
    loadingOverlayComponent: CustomLoadingOverlay,
    onGridSizeChanged: () => {
      gridOptions.api?.sizeColumnsToFit();
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const items = [
    {
      id: 1,
      title: () => "مشاهده جزئیات اوراق",
      icon: () => <Eye />,
      action: (value: any) => {
        window.open(`${routes.detail}/${value?.isin}`, "_blank");
      }
    },
    {
      id: 2,
      title: () => "حذف از دیدبان",
      icon: () => <Microscope className="w-[10px] h-[10px]" />,
      action: (v: any) => deleteSymbolFn(v)
    }
  ];

  const [gridApi, setGridApi] = useState<any>();

  useEffect(() => {
    gridApi?.redrawRows();
  }, [couponSideBarId, gridApi]);

  const onGridReady = (p: any) => {
    setGridApi(p.api);
  };

  const rowStyle = (p: any) => {
    if (p?.node?.id === couponSideBarId) {
      return { border: "1px solid #BDBDBD" };
    }
    return undefined;
  };

  const [inputTitle, setInputTitle] = useState("افزودن اوراق");

  // if while get watch list data error occurs, run this code
  if (isError) {
    <div className="flex justify-center items-center grow">
      <span>خطایی رخ داده است!</span>
    </div>;
  }

  return (
    <div className="grow  flex h-full px-4 pb-2 gap-2">
      <SideBar setSideBarId={setCouponSideBarId} isin={couponSideBarId} />
      <div className="grow h-full max-w-full flex-col">
        <div className="h-8 flex justify-between">
          <WatchListIsinInput
            watchListDataSearch={watchListDataSearch}
            endAdornment={<AddIcon className="mr-[3px] w-4 h-4" />}
            className="h-8"
            title={inputTitle}
            value={isinInputValue?.symbol}
            preserveErrorMessage={false}
            onChange={v => {
              if (v?.isInWatchlist) return;
              if (v?.isin) addToWatchList(v?.isin ?? "", v?.symbol ?? "", postAddSymbolToWatchList, refetch);
              setIsinInputValue(v);
            }}
            onFocus={() => setInputTitle("")}
          />
          {!isLoading && (
            <div className={twMerge("flex gap-[2px] items-end", tableData?.length && " items-center")}>
              <div className="text-xs text-mainText">{tableData?.length}</div>
              <div className="text-[10px] text-secondaryText">نماد</div>
            </div>
          )}
        </div>
        <div className="h-[calc(100%-48px)] flex flex-col mt-2">
          {!isLoading && tableData?.length ? (
            <Table
              columnDefs={columnDefsData}
              onSort={onSort}
              // className={styles.watchListTableContainer}
              getRowStyle={rowStyle}
              getRowId={getRowId as any}
              menuItems={items}
              gridOptions={gridOptions}
              data={tableData}
              onCellDoubleClicked={(cell: CellDoubleClickedEvent<any>) =>
                window.open(`${routes.detail}/${cell.data?.isin}`, "_blank")
              }
              onGridReady={onGridReady}
            />
          ) : (
            <NoData
              columnDefs={columnDefsData}
              isinInputValue={isinInputValue}
              onSubmit={onSubmit}
              setIsinInputValue={setIsinInputValue}
            />
          )}

          <div className="h-[40px] bg-[#343438] border-t border-[#545454] justify-between rounded-b-[4px] flex items-center px-2">
            {!!totalCount && totalCount > 0 && (
              <div className="w-fit">
                <Pagination
                  showItemsCounter
                  setPageNumber={setPageNumber}
                  pageNumber={pageNumber}
                  totalCount={totalCount ?? 0}
                  rowPerPage={pageSize}
                  onChangeRow={e => {
                    setPageSize(e);
                    setPageNumber(0);
                  }}
                />
              </div>
            )}
            <Excel className="cursor-pointer h-6 w-6" onClick={onExportClick} />
          </div>
        </div>
      </div>
    </div>
  );
}

export default WatchList;
