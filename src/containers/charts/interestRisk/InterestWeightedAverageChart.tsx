"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { useMemo } from "react";
import { getSeriesOfInterestRiskFormat, getSummaryInterestOptions } from "@/containers/cards/util";
import HighChart from "@/components/molecules/highChart/HighChart";
import { useGetRiskFreeRateHistoryChart } from "@/queries/FundChartsAPI";
import { Spinner } from "@/components/atoms/spinner";
import ChartWrapper from "../chartLayouts/ChartWrapper";
import SummaryBox from "../chartLayouts/SummaryBox";

function InterestWeightedAverageChart() {
  const { data: raw, isLoading } = useGetRiskFreeRateHistoryChart();
  const { data } = raw || {};

  const { graphPoints = [], yearAverage = 0 } = data || {};
  const summary = getSummaryInterestOptions(yearAverage, 0);

  const options = useMemo(
    () =>
      getSeriesOfInterestRiskFormat({
        graphPoints,
        field1: {
          id: "riskFreeRate",
          title: "نرخ بهره بدون ریسک"
        },
        hasPercent: true
      }),
    [data]
  );

  return (
    <ChartWrapper summary={<SummaryBox hasPercent data={summary} showMonth3={false} />}>
      {raw && <HighChart options={options} showTunedPeriod />}
      {isLoading && (
        <div className="w-full h-full m-auto justify-center items-center flex relative top-[-46px]">
          <Spinner />
        </div>
      )}
    </ChartWrapper>
  );
}

export default InterestWeightedAverageChart;
