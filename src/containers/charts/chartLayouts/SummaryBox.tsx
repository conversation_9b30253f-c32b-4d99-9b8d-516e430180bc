/* eslint-disable react/no-array-index-key */
import { memo } from "react";
import { twMerge } from "tailwind-merge";
import { toFixed } from "@/containers/tradeHistory/util";
import DateFilter from "@/components/molecules/DateFilter";
import { ISummaryBoxProps } from "../type";

function SummaryBox(props: ISummaryBoxProps) {
  const { data = [], hasPercent, showMonth3 } = props;

  return (
    <div className="flex justify-between p-3 pb-0">
      <div className="flex text-sm gap-3 leading-4 p-2 pt-1.5 rounded-lg right-3 top-1 z-10 bg-dark_black9 text-white">
        <div className="text-sm">میانگین گذشته:</div>
        <div className="flex gap-4 items-center" aria-label="history-averages">
          {data.map((i, k) => (
            <span key={k} className="flex gap-1 items-center" aria-label={`history-averages-item-${k}`}>
              <i className={twMerge(`flex w-3 h-3 rounded-sm`)} style={{ background: i.color }} />
              <span className="leading-4 text-xs ltr">
                {hasPercent ? `٪ ${toFixed(i.value * 100)}` : toFixed(i.value)}
              </span>
            </span>
          ))}
        </div>
      </div>
      <div className="z-50">
        <DateFilter showMonth3={showMonth3} />
      </div>
    </div>
  );
}

export default memo(SummaryBox);
