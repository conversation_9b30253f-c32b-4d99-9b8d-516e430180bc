"use client";

/* eslint-disable react-hooks/exhaustive-deps */

import { parseAsString, useQueryStates } from "nuqs";
import Checkbox from "@/components/atoms/checkbox/Checkbox";
import SwitchButton from "@/components/molecules/switchButton";
import Table from "@/assets/icons/transaction-table.svg";
import Chart from "@/assets/icons/transaction-chart.svg";

import TransactionsTable from "./TransactionsTable";

import TransactionDateFilter from "./TransactionDateFilter";
import TransactionsChart from "./TransactionsChart";

export const switchItems = [
  {
    title: "جدول",
    id: 1,
    icon: <Table />
  },
  {
    title: "نمودار",
    id: 2,
    icon: <Chart />
  }
];

function Transactions() {
  const [queryStates, setQueryStates] = useQueryStates({
    purchaseType: parseAsString.withDefault("0"),
    transactionType: parseAsString.withDefault("2")
  });

  const transactionType = queryStates?.transactionType;

  const togglePurchaseType = (type: string) => {
    const currentTypes =
      queryStates.purchaseType === "0" ? ["100", "200"] : queryStates.purchaseType.split(",").filter(Boolean);

    const isSelected = currentTypes.includes(type);

    const newTypes = isSelected ? currentTypes.filter(t => t !== type) : [...currentTypes, type];

    const has100 = newTypes.includes("100");
    const has200 = newTypes.includes("200");

    if ((has100 && has200) || newTypes.length === 0) {
      setQueryStates({ purchaseType: "0" });
    } else {
      setQueryStates({ purchaseType: newTypes.join(",") });
    }
  };

  const isChecked = (type: string) => {
    const value = queryStates.purchaseType;
    return value === "0" || value.split(",").includes(type);
  };

  return (
    <div className="flex grow relative">
      <div className=" h-full grow flex flex-col gap-1">
        <div className="flex items-center justify-between bg-[#343438] rounded-tl-lg rounded-tr-lg p-1">
          <SwitchButton
            switchItems={switchItems}
            selectedItemId={transactionType ? Number(transactionType) : 0}
            onSelectedItemChange={itemID => {
              if (itemID) setQueryStates({ transactionType: itemID.toString() });
            }}
          />
          <div className="rounded p-1 pl-2 flex items-center gap-4 bg-[#28282C]">
            <Checkbox
              text="قراردادها"
              variant="filledBlue"
              checked={isChecked("100")}
              onChange={() => togglePurchaseType("100")}
            />
            <Checkbox
              text="معاملات تابلو"
              variant="filledBlue"
              checked={isChecked("200")}
              onChange={() => togglePurchaseType("200")}
            />
          </div>
          <TransactionDateFilter />
        </div>
        {transactionType === "1" && (
          <div className="bg-backgroundDarkRow grow h-full">
            <TransactionsTable />
          </div>
        )}
        {transactionType === "2" && <TransactionsChart />}
      </div>
    </div>
  );
}

export default Transactions;
