"use client";

/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react/button-has-type */
/* eslint-disable no-nested-ternary */

import Pagination from "@/components/molecules/pagination/Pagination";
import { Table } from "@/components/organisms/Table";
import { CustomLoadingOverlay } from "@/components/organisms/Table/utils";
import { GridOptions } from "ag-grid-community";
import { useEffect, useMemo, useState } from "react";
import { useGetTransactionsTable } from "@/queries/TransactionsAPI";
import { useVarDateFilterStore } from "@/store/varDateFilter";
import { SortState } from "@/components/organisms/Table/types";
import { parseAsString, useQueryStates } from "nuqs";
import Input from "@/components/atoms/input";
import NoData from "@/components/organisms/Table/NoData";
import Search from "@/assets/icons/detail-search.svg";
import { columnDefs, sortKeyName } from "./utils";

function TransactionsTable() {
  const { period } = useVarDateFilterStore();
  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(15);
  const [filterSymbol, setFilterSymbol] = useState("");

  const [queryStates] = useQueryStates({
    purchaseType: parseAsString.withDefault("100")
  });

  const [sorts, setSorts] = useState<{ [key: string]: string }>({ SortTransactionDate: "Asc" });

  const [params, setParams] = useState({
    ...sorts,
    pageSize,
    pageNumber: pageNumber + 1
  });

  const columnDefsData = useMemo(() => columnDefs(), []);

  const {
    data: raw,
    isLoading,
    isError
  } = useGetTransactionsTable({
    fromDate: period?.fromDate,
    toDate: period?.toDate,
    symbol: filterSymbol,
    filterByPurchaseType: queryStates?.purchaseType,
    ...params
  });

  const tableData = isLoading ? null : raw?.data?.items?.length ? raw?.data?.items : [];
  const totalCount = raw?.data?.totalCount;

  useEffect(() => {
    setParams({
      ...sorts,
      pageSize,
      pageNumber: pageNumber + 1
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageNumber, sorts, pageSize]);

  const onSort = (sortState?: SortState) => {
    let sortType = !!sorts && Object.values(sorts)?.at(0);
    // eslint-disable-next-line no-nested-ternary
    sortType = !sortType ? "Asc" : sortType === "Asc" ? "Desc" : "Asc";

    const sort = { [sortKeyName[sortState?.colId ?? ""]]: sortType };
    setSorts(sort);
  };

  const onSearch = (value: string) => {
    setFilterSymbol(value);
  };

  const gridOptions: GridOptions = {
    enableRtl: true,
    sortingOrder: ["asc", "desc"],
    loadingOverlayComponent: CustomLoadingOverlay,
    onGridSizeChanged: () => {
      gridOptions.api?.sizeColumnsToFit();
    }
  };

  return (
    <div className="grow h-full flex flex-col max-w-full flex-col bg-[#343438] p-1 rounded-br-lg rounded-bl-lg">
      <Input
        value={filterSymbol}
        onChange={onSearch}
        preserveErrorMessage={false}
        inputSize="small"
        placeHolder="جستجو (نام نماد)"
        startAdornment={<Search />}
        inputWrapperClassName="max-w-[320px]"
        data-test="531cbdaa-8c85-4833-9354-f242a1279966"
      />

      <div className="flex h-full flex-col">
        <div className="flex-1">
          {isLoading || (!isLoading && tableData?.length) ? (
            <Table
              columnDefs={columnDefsData}
              onSort={onSort}
              gridOptions={gridOptions}
              data={tableData}
              headerColor="#1F1F22"
              loadingOverlayComponent={CustomLoadingOverlay}
              noRowsOverlayComponent={CustomLoadingOverlay}
            />
          ) : (
            !isLoading &&
            !tableData?.length && (
              <NoData
                columnDefs={columnDefsData}
                headerColor="#1F1F22"
                isError={isError}
                errorTitle="ارتباط با سرور برقرار نیست"
                noRowsTitle="نمادی یافت نشد"
              />
            )
          )}
        </div>

        <div className="h-[40px] bg-[#343438] border-t border-[#545454] justify-between rounded-b-[4px] flex items-center px-2">
          {!!totalCount && totalCount > 0 && (
            <div className="w-fit">
              <Pagination
                showItemsCounter
                setPageNumber={setPageNumber}
                pageNumber={pageNumber}
                totalCount={totalCount ?? 0}
                rowPerPage={pageSize}
                onChangeRow={e => {
                  setPageSize(e);
                  setPageNumber(0);
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default TransactionsTable;
