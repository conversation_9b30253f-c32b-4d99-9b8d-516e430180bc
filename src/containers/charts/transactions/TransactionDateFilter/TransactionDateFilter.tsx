/* eslint-disable react/no-unstable-nested-components */

import Calendar from "@/assets/icons/calendar-transaction.svg";
import { DatePickerWrapper } from "@/components/organisms/datePicker";
import { IDatePickerWrapperRefProps } from "@/components/organisms/datePicker/types";
import RangeMonthPickerWrapper from "@/components/organisms/monthDatePicker/RangeMonthPickerWrapper";
import { Select } from "@/components/atoms/select";
import { TRangeValue } from "@/components/organisms/monthDatePicker/types";
import { useVarDateFilterStore } from "@/store/varDateFilter";
import { dateConverter } from "@/utils/DateHelper";
import { components } from "react-select";
import ChevronDown from "@/assets/icons/chevron-down.svg";
import { memo, useCallback, useEffect, useRef, useState } from "react";

import { FILTER_TIME_FUTURE, FILTER_TIME_PAST, getPeriodByRange, getPrevDate } from "./utils";
import selectStyles from "./styles";

// eslint-disable-next-line react/function-component-definition
const CustomSingleValue = (prefix: string) => (props: any) => (
  // eslint-disable-next-line react/destructuring-assignment
  <components.SingleValue {...props}>{`${prefix} ${props.data.label}`}</components.SingleValue>
);

function TransactionDateFilter({
  defaultTimeKey,
  showDatePickerMonthly
}: {
  defaultTimeKey?: { id: string; title: string };
  showDatePickerMonthly?: boolean;
}) {
  const datePickerRef = useRef<IDatePickerWrapperRefProps>(null);
  const [initialDate, setInitialDate] = useState<Date[] | undefined>();
  const [pastValue, setPastValue] = useState<FILTER_TIME_PAST | "now">(FILTER_TIME_PAST.Month6);
  const [futureValue, setFutureValue] = useState<FILTER_TIME_FUTURE>(FILTER_TIME_FUTURE.Yearly);
  const { setPeriod, setActiveTab } = useVarDateFilterStore();

  const onSwitch = useCallback(
    (v: FILTER_TIME_PAST | FILTER_TIME_FUTURE | "now", isPast?: boolean) => {
      const fromDate: FILTER_TIME_PAST | "now" = isPast ? (v as FILTER_TIME_PAST | "now") : pastValue;
      const toDate: FILTER_TIME_FUTURE = isPast ? futureValue : (v as FILTER_TIME_FUTURE);

      const date = getPeriodByRange(fromDate, toDate);

      setPeriod({ fromDate: date?.fromDate, toDate: date?.toDate });

      if (isPast) {
        setPastValue(v as FILTER_TIME_PAST | "now");
      } else {
        setFutureValue(v as FILTER_TIME_FUTURE);
      }
    },
    [pastValue, futureValue]
  );

  useEffect(() => {
    onSwitch(FILTER_TIME_FUTURE.Yearly);
  }, []);

  useEffect(() => {
    if (defaultTimeKey) {
      setActiveTab(defaultTimeKey);
      const date = getPrevDate(defaultTimeKey?.id);
      setPeriod({ fromDate: date });
    }
  }, [defaultTimeKey]);

  const onDateChange = (e: TRangeValue) => {
    setPeriod({ fromDate: e?.[0], toDate: e?.[1] });
  };

  const pastItems = [
    { value: FILTER_TIME_PAST.Month1, label: "۱ ماه گذشته" },
    { value: FILTER_TIME_PAST.Month3, label: "3 ماه گذشته" },
    { value: FILTER_TIME_PAST.Month6, label: "۶ ماه گذشته" },
    { value: FILTER_TIME_PAST.Now, label: "امروز" }
  ];

  const futureItems = [
    { value: FILTER_TIME_FUTURE.Month1, label: "1 ماه آینده" },
    { value: FILTER_TIME_FUTURE.Month3, label: "3 ماه آینده" },
    { value: FILTER_TIME_FUTURE.Month6, label: "6 ماه آینده" },
    { value: FILTER_TIME_FUTURE.Yearly, label: "1 سال آینده" },
    { value: FILTER_TIME_FUTURE.ThreeYearly, label: "3 سال آینده" }
  ];

  return (
    <div className="flex items-center gap-1">
      {!showDatePickerMonthly ? (
        <DatePickerWrapper
          isRange
          ref={datePickerRef}
          closeOnConfirm={false}
          hasFooter
          initialValue={initialDate}
          config={{
            locale: "fa-IR",
            weekends: ["friday"],
            maxDate: new Date()
          }}
          className="-top-[6px]"
          onConfirm={(date?: Date[]) => {
            if (date && date[0] && date[1] && date !== initialDate) {
              setInitialDate(date);
              setPeriod({
                fromDate: dateConverter(date[0]).calendar("gregory").format("YYYY-MM-DD"),
                toDate: dateConverter(date[1]).calendar("gregory").format("YYYY-MM-DD")
              });

              datePickerRef.current?.close();

              // const f = filterTimeItems.find(i => i.id === FILTER_TIME.DateRange);

              // if (f) {
              //   onSwitch(f);
              // }
            }
          }}
          onCancel={() => {
            setInitialDate(undefined);
            // onSwitch(filterTimeItems[1]);
          }}
        >
          <Calendar className="cursor-pointer" />
        </DatePickerWrapper>
      ) : (
        <RangeMonthPickerWrapper onDateSelect={onDateChange} initialValue={undefined}>
          <Calendar className="cursor-pointer" />
        </RangeMonthPickerWrapper>
      )}

      <div className="min-w-[120px]">
        <Select
          items={pastItems}
          components={{
            SingleValue: CustomSingleValue("از"),
            DropdownIndicator: () => (
              <div className="px-2">
                <ChevronDown className="size-4 text-[#EFEFEF]" />
              </div>
            )
          }}
          value={pastItems?.find(item => item?.value === pastValue)}
          styles={selectStyles}
          onChange={(v: any) => {
            onSwitch(v?.value, true);
          }}
        />
      </div>

      <div className="min-w-[120px]">
        <Select
          components={{
            SingleValue: CustomSingleValue("تا"),
            DropdownIndicator: () => (
              <div className="px-2">
                <ChevronDown className="size-4 text-[#EFEFEF]" />
              </div>
            )
          }}
          items={futureItems}
          value={futureItems?.find(item => item?.value === futureValue)}
          styles={selectStyles}
          onChange={(v: any) => {
            onSwitch(v?.value);
          }}
        />
      </div>
    </div>
  );
}

export default memo(TransactionDateFilter);
