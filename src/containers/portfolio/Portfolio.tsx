import React, { useMemo } from "react";
import PortfolioSlider from "@/containers/portfolio/components/PortfolioSlider/PortfolioSlider";
import { useGetPiePortfolio } from "@/queries/FundChartsAPI";
import PieChart from "@/components/molecules/PieChart";
import randomHexColorCode from "@/containers/portfolio/components/PortfolioSlider/utils";
import { Spinner } from "@/components/atoms/spinner";
import PortfolioTable from "@/containers/portfolio/components/PortfolioTable/PortfolioTable";
import { toFixedInPercent } from "@/utils/helpers";

function Portfolio() {
  const { data: pieData, isLoading } = useGetPiePortfolio();
  const topBarData = useMemo(
    () =>
      pieData?.data?.items?.sort((a, b) => Number(b?.portfolioWeightInPercents) - Number(a?.portfolioWeightInPercents)),
    [pieData]
  );

  return (
    <div className="h-full flex flex-col">
      <div className="w-full flex px-2">
        <div className="flex items-center justify-start bg-cardBackground rounded-r-xl max-w-[320px] 2xl:max-w-[450px] 2xl:w-[19%] w-[24%] pr-1.5">
          {topBarData && topBarData?.length > 0 && (
            <PieChart
              id="pieChart"
              width={116}
              height={116}
              data={topBarData?.map((item, index) => ({
                name: item?.symbol!,
                y: item.portfolioWeightInPercents!,
                color: randomHexColorCode(index)
              }))}
            />
          )}
          {!pieData?.data?.items?.length && <div className="bg-dark_black7 rounded-full w-24 h-24" />}
          {pieData?.data?.items?.length && (
            <div className="pr-1.5 pl-4 w-full">
              <div className="text-sm font-bold">ارزش کل پرتفوی</div>
              {pieData?.data?.portfolioSheetsValuesSum && (
                <div className="text-xs pt-5">
                  <span className="font-bold text-xl">
                    {(Number(pieData?.data?.portfolioSheetsValuesSum) / 1000000000)?.toFixed(3)}
                  </span>
                  میلیارد ریال
                </div>
              )}
              {!pieData?.data?.portfolioSheetsValuesSum && <div className="text-xs pt-5"> 0 </div>}

              {pieData?.data?.portfolioPreferredYtmInPercent && (
                <div className="mt-5 flex items-center justify-between pt-2 border-t border-t-[#676767] w-full">
                  <span className="text-neutral-50">YTM پرتفوی</span>
                  <span className="text-sm text-[#EFEFEF]">
                    {toFixedInPercent(pieData?.data?.portfolioPreferredYtmInPercent, 1)}
                  </span>
                </div>
              )}
            </div>
          )}
          {isLoading && (
            <div className="w-32 h-full m-auto justify-center items-center flex">
              <Spinner className="scale-150" />
            </div>
          )}
        </div>
        <div className="w-[76%] 2xl:w-[81%] bg-[#1F1F22] rounded-l-xl min-h-32">
          {!isLoading && topBarData && <PortfolioSlider data={topBarData} />}
          {isLoading && (
            <div className="w-full h-full m-auto justify-center items-center flex">
              <Spinner className="scale-150" />
            </div>
          )}
        </div>
      </div>
      <PortfolioTable />
    </div>
  );
}

export default Portfolio;
