import { ColDef, IDetailCellRendererParams } from "@ag-grid-community/core";
import { twMerge } from "tailwind-merge";
import { ICustomGroupCellRendererProps } from "@/components/organisms/Table/CollapsibleTable/CollapsibleTableTypes";
import { FlatTransaction } from "@/queries/TransactionsAPI/types";
import { IAccount, ICallRecord } from "@/components/organisms/Table/CollapsibleTable/storybook/CollapsibleStoryRender";
import { dateConverter } from "@/utils/DateHelper";
import { commaSeparator } from "@/utils/helpers";

export function CustomGroupCellRenderer({ ...restProps }: ICustomGroupCellRendererProps) {
  const AgGroupCellRenderer = restProps?.api?.frameworkOverrides?.frameworkComponents?.agGroupCellRenderer;
  return (
    <>
      <div className={twMerge("h-[70px] w-1  absolute z-50 -top-1 right-0 -mb-2.5")} />
      <AgGroupCellRenderer {...restProps} />
    </>
  );
}

export function renderChildTableConfig() {
  return {
    detailGridOptions: {
      columnDefs: [
        {
          field: "purchaseType",
          headerName: "نوع خرید",
          cellRenderer: ({ data }: { data: FlatTransaction }) => (
            <div className="pr-1">
              {/* eslint-disable-next-line no-nested-ternary */}
              {data?.purchaseType ? (data?.purchaseType === 100 ? "قراردادی" : "معاملات تابلو") : "-"}
            </div>
          )
        },
        {
          field: "contractNumber",
          headerName: "شماره قرارداد"
        },
        {
          field: "purchaseDate",
          headerName: "تاریخ شروع قرارداد",
          cellRenderer: ({ data }: { data: { purchaseDate?: string } }) => (
            <div>{data?.purchaseDate ? dateConverter(data?.purchaseDate).format("YYYY/MM/DD") : "---"}</div>
          )
        },
        {
          field: "purchasePrice",
          headerName: "قیمت خرید (تومان)"
        },
        {
          field: "amount",
          headerName: "تعداد خریداری شده"
        },
        {
          field: "contractDue",
          headerName: "تاریخ پایان قرارداد",
          cellRenderer: ({ data }: { data: { contractDue?: string } }) => (
            <div>{data?.contractDue ? dateConverter(data?.contractDue).format("YYYY/MM/DD") : "---"}</div>
          )
        },
        {
          field: "AgreedSalePrice",
          headerName: "قیمت فروش (تومان)",
          cellRenderer: ({ data }: { data: { AgreedSalePrice?: string } }) => data?.AgreedSalePrice ?? "-"
        },
        {
          field: "remainingAmount",
          headerName: "تعداد باقی‌مانده"
        },
        {
          field: "ytmPercent",
          headerName: "YTM",
          minWidth: 150,
          cellRenderer: ({ data }: { data: { ytmPercent?: number } }) => (
            <div>{data?.ytmPercent ? `${commaSeparator(data?.ytmPercent, 3)}%` : "-"}</div>
          )
        }
      ]?.reverse(),
      defaultColDef: {
        flex: 1
      },
      rowHeight: 40
    },
    getDetailRowData: params => {
      params.successCallback(params.data.callRecords);
    }
  } as IDetailCellRendererParams<IAccount, ICallRecord>;
}

export const columnDefs = (): ColDef[] => [
  {
    headerName: "نام نماد",
    unSortIcon: true,
    sortable: true,
    field: "symbol",
    cellRenderer: (props: any) => <CustomGroupCellRenderer {...props} />
  },
  {
    field: "instrumentName",
    headerName: "نام کامل"
  },
  {
    field: "ytmPercent",
    headerName: "YTM",
    cellRenderer: ({ data }: { data: { ytmPercent?: number } }) => (
      <div>{data?.ytmPercent ? `${commaSeparator(data?.ytmPercent, 3)}%` : "-"}</div>
    )
  },
  {
    field: "portfolioSheetsValue",
    headerName: "ارزش نماد (تومان)",
    cellRenderer: ({ data }: { data: { portfolioSheetsValue?: string } }) => (
      <div>{data?.portfolioSheetsValue ? commaSeparator(data?.portfolioSheetsValue) : "-"}</div>
    )
  }
];

export function LoadingRenderer() {
  return <h1 style={{ padding: "20px", color: "white" }}>Loading...</h1>;
}

export const getRowId = (params: { data: { key: string } }) => params.data.key;
